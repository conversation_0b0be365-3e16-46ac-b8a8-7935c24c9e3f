* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background: #f4f7f8;
  color: #333;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.navbar {
  background: #004aad;
  color: white;
  display: flex;
  align-items: center;
  padding: 15px 20px;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 999;
}

.logo {
  font-weight: bold;
  font-size: 1.5rem;
}

.nav-links {
  list-style: none;
  display: flex;
  gap: 25px;
}

.nav-links li a {
  color: white;
  text-decoration: none;
  font-weight: 500;
}

.nav-links li a:hover {
  text-decoration: underline;
}

.lang-switch {
  background: white;
  border: none;
  padding: 8px 14px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  color: #004aad;
  transition: background-color 0.3s ease;
}

.lang-switch:hover {
  background-color: #e0e0e0;
}

main {
  flex: 1;
  padding: 40px 20px;
  max-width: 1200px;
  margin: auto;
}

.hero {
  background: linear-gradient(90deg, #004aadcc, #007bffcc), url('https://via.placeholder.com/1200x400') center/cover no-repeat;
  padding: 80px 20px;
  color: white;
  text-align: center;
  border-radius: 8px;
}

.hero-content h1 {
  font-size: 2.8rem;
  margin-bottom: 20px;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
}

.cta-button {
  background-color: #ff7f50;
  padding: 14px 32px;
  border: none;
  border-radius: 30px;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  text-decoration: none;
}

.page h1 {
  text-align: center;
  margin-bottom: 30px;
  font-size: 2rem;
  color: #00b894;
  
}

.services-grid,
.projects-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.service-card,
.project-card {
  background: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 0.1);
}

.footer {
  background: #004aad;
  color: white;
  text-align: center;
  padding: 20px;
  font-size: 0.9rem;
  margin-top: auto;
}

.social-links a {
  color: white;
  text-decoration: none;
  margin: 0 10px;
}

.social-links a:hover {
  text-decoration: underline;
}
body {
  font-family: 'Segoe UI', sans-serif;
  margin: 0;
  padding: 0;
  background: #f9f9f9;
  direction: rtl; /* يدعم اللغة العربية */
}

.navbar {
  display: flex;
  justify-content: space-between;
  background-color: #222;
  color: white;
  padding: 10px 20px;
}
.navbar .nav-links {
  display: flex;
  list-style: none;
  gap: 15px;
}
.navbar .nav-links li a {
  color: white;
  text-decoration: none;
}

.page {
  padding: 40px 20px;
}

input, textarea {
  width: 100%;
  padding: 10px;
  margin-top: 6px;
  margin-bottom: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
button {
  padding: 10px 20px;
  background-color: #00b894;
  border: none;
  color: white;
  cursor: pointer;
  border-radius: 4px;
}
body {
  font-family: 'Segoe UI', sans-serif;
  margin: 0;
  padding: 0;
  background: #f9f9f9;
  color: #222;
  direction: rtl;
}
body.dark {
  background-color: #1c1c1c;
  color: #eee;
}
.navbar, .footer {
  background-color: #222;
  color: white;
  padding: 10px 20px;
}
.footer .social a {
  color: #ccc;
  margin-left: 10px;
}
button {
  margin-right: 10px;
}
.hero-section {
  text-align: center;
  padding: 60px 20px;
  background: linear-gradient(to right, #00b894, #0984e3);
  color: white;
}
.cta-button {
  display: inline-block;
  background: white;
  color: #00b894;
  padding: 10px 20px;
  margin-top: 20px;
  text-decoration: none;
  font-weight: bold;
  border-radius: 6px;
}
.values-section, .about-preview, .services-preview, .contact-cta {
  padding: 40px 20px;
  text-align: center;
}
.service-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
  margin: 20px 0;
}
.service-cards .card {
  background: #eee;
  padding: 20px;
  border-radius: 8px;
  min-width: 150px;
}
.link-button {
  display: inline-block;
  margin-top: 15px;
  text-decoration: underline;
  color: #0984e3;
}
.contact-cta {
  background-color: #222;
  color: white;
}


/* ====================== صفحة الخدمات ====================== */
.page.services {
  padding: 60px 20px 100px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #fff;
  text-align: center;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  min-height: 100vh;
  box-sizing: border-box;
}

.services-title {
  font-size: 3.2rem;
  font-weight: 900;
  margin-bottom: 50px;
  text-shadow: 0 3px 10px rgba(0, 0, 0, 0.7);
  user-select: none;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
}

/* ======== بطاقة الخدمة ======== */
.service-card {
  background: rgba(255 255 255 / 0.08);
  border-radius: 20px;
  padding: 30px 25px;
  box-shadow: 0 10px 35px rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(12px);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  user-select: none;
}

.service-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.45);
}

.icon {
  width: 64px;
  height: 64px;
  margin-bottom: 25px;
  user-select: none;
  filter: drop-shadow(0 0 3px rgba(255 255 255 / 0.8));
}

.service-card h3 {
  font-size: 1.7rem;
  font-weight: 900;
  margin-bottom: 15px;
  text-shadow: 1px 1px 6px rgba(0, 0, 0, 0.6);
}

.service-card p {
  font-size: 1.15rem;
  line-height: 1.5;
  margin-bottom: 25px;
  color: #f0f0f0cc;
  min-height: 70px;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.45);
}

/* زر تعلم أكثر */
.btn-learn-more {
  background: #ffca28;
  color: #222;
  border: none;
  padding: 12px 32px;
  border-radius: 30px;
  font-weight: 700;
  font-size: 1rem;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 6px 15px rgba(255, 202, 40, 0.7);
  cursor: pointer;
  user-select: none;
}

.btn-learn-more:hover {
  background: #ffb300;
  box-shadow: 0 10px 30px rgba(255, 179, 0, 0.9);
}

/* ========== النافذة المنبثقة (Modal) ========== */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(10, 10, 10, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  padding: 20px;
  box-sizing: border-box;
}

.modal-content {
  background: linear-gradient(145deg, #283e51, #485563);
  border-radius: 25px;
  padding: 40px 35px 50px;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.7);
  position: relative;
  color: #fff;
  text-align: center;
  user-select: none;
}

.modal-close {
  position: absolute;
  top: 18px;
  right: 18px;
  background: transparent;
  border: none;
  font-size: 2.5rem;
  color: #fff;
  cursor: pointer;
  font-weight: 700;
  line-height: 1;
  padding: 0;
  user-select: none;
  transition: color 0.3s ease;
}

.modal-close:hover {
  color: #ffca28;
}

.modal-icon {
  margin-bottom: 25px;
  width: 80px;
  height: 80px;
  margin-left: auto;
  margin-right: auto;
  filter: drop-shadow(0 0 5px rgba(255 255 255 / 0.9));
}

.modal-content h2 {
  font-size: 2.4rem;
  font-weight: 900;
  margin-bottom: 20px;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
}

.modal-content p {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #ddd;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.6);
}

/* =========== RESPONSIVE ============ */
@media (max-width: 768px) {
  .services-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 30px;
  }

  .page.services {
    padding: 40px 15px 80px;
  }

  .services-title {
    font-size: 2.6rem;
  }

  .service-card h3 {
    font-size: 1.5rem;
  }

  .service-card p {
    font-size: 1rem;
  }

  .btn-learn-more {
    padding: 10px 25px;
    font-size: 0.9rem;
  }

  .modal-content {
    padding: 30px 20px 40px;
  }

  .modal-content h2 {
    font-size: 2rem;
  }

  .modal-content p {
    font-size: 1rem;
  }
}

@media (max-width: 400px) {
  .services-title {
    font-size: 2rem;
  }

  .service-card h3 {
    font-size: 1.3rem;
  }

  .btn-learn-more {
    padding: 8px 20px;
    font-size: 0.85rem;
  }
}


/* الصفحة العامة */
.page.projects {
  padding: 60px 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #e0f0ff, #d3e7ff);
  color: #222;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* العنوان */
.page.projects > h1 {
  font-size: 3rem;
  margin-bottom: 50px;
  color: #1a3f8f;
  font-weight: 900;
  letter-spacing: 0.1em;
  user-select: none;
  text-shadow: 0 2px 6px rgba(26, 63, 143, 0.3);
}

/* شبكة المشاريع */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  width: 100%;
  max-width: 1300px;
}

/* البطاقة */
.project-card {
  background: white;
  border-radius: 25px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(26, 63, 143, 0.15);
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.project-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 20px 45px rgba(26, 63, 143, 0.3);
}

/* غلاف الصورة */
.img-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 25px 25px 0 0;
  height: 200px;
}

.img-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.project-card:hover .img-wrapper img {
  transform: scale(1.05);
}

/* طبقة التراكب */
.overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgba(26, 63, 143, 0.85) 0%, transparent 100%);
  color: white;
  padding: 20px 25px;
  opacity: 0;
  transition: opacity 0.4s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  border-radius: 0 0 25px 25px;
  text-align: center;
}

.project-card:hover .overlay {
  opacity: 1;
}

/* عنوان المشروع */
.overlay h3 {
  font-size: 1.6rem;
  margin-bottom: 10px;
  font-weight: 700;
  letter-spacing: 0.05em;
}

/* وصف المشروع */
.overlay p {
  font-size: 1rem;
  font-style: italic;
  margin-bottom: 15px;
  line-height: 1.4;
}

/* زر التفاصيل */
.btn-details {
  align-self: center;
  padding: 10px 25px;
  font-weight: 600;
  border-radius: 50px;
  border: none;
  background: #fff;
  color: #1a3f8f;
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
  user-select: none;
}

.btn-details:hover {
  background-color: #1a3f8f;
  color: #fff;
}

/* استجابة الشاشات الصغيرة */
@media (max-width: 600px) {
  .page.projects {
    padding: 40px 10px;
  }

  .page.projects > h1 {
    font-size: 2.4rem;
    margin-bottom: 30px;
  }

  .img-wrapper {
    height: 180px;
  }

  .overlay h3 {
    font-size: 1.3rem;
  }

  .overlay p {
    font-size: 0.9rem;
  }

  .btn-details {
    padding: 8px 20px;
    font-size: 0.9rem;
  }
}

/* الخلفية المظلمة للمودال */
.modal-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0,0,0,0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

/* محتوى المودال */
.modal-content {
  background: #fff;
  border-radius: 15px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  padding: 30px 25px;
  position: relative;
  text-align: right; /* للغة العربية، غيرها للإنجليزية */
  color: #222;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 1.1rem;
  line-height: 1.6;
  font-weight: 400;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* العنوان داخل المودال */
.modal-content h2 {
  margin-bottom: 15px;
  color: #1a3f8f;
  font-weight: 700;
  font-size: 1.8rem;
  letter-spacing: 0.05em;
}

/* صورة المشروع داخل المودال */
.modal-content img {
  width: 100%;
  border-radius: 12px;
  margin-bottom: 20px;
  object-fit: cover;
  max-height: 300px;
}

/* زر إغلاق المودال */
.modal-content button.close-modal {
  position: absolute;
  top: 15px;
  right: 15px;
  background: transparent;
  border: none;
  font-size: 2rem;
  font-weight: 700;
  color: #151313;
  cursor: pointer;
}

/* استجابة المودال للشاشات الصغيرة */
@media (max-width: 600px) {
  .modal-content {
    padding: 20px 15px;
    font-size: 1rem;
  }
  .modal-content h2 {
    font-size: 1.4rem;
  }
}


