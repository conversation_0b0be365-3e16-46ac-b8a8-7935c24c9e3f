import React, { useContext } from 'react';
import { <PERSON> } from 'react-router-dom';
import { LanguageContext } from '../context/LanguageContext';
import { ThemeContext } from '../context/ThemeContext';

export default function Navbar() {
  const { lang, toggleLang } = useContext(LanguageContext);
  const { dark, toggleTheme } = useContext(ThemeContext);

  const t = {
    ar: { home: "الرئيسية", about: "من نحن", services: "الخدمات", projects: "المشاريع", contact: "تواصل" },
    en: { home: "Home", about: "About", services: "Services", projects: "Projects", contact: "Contact" }
  };

  return (
    <nav className="navbar">
      <h2 className="logo">SmartTech AI</h2>
      <ul className="nav-links">
        <li><Link to="/">{t[lang].home}</Link></li>
        <li><Link to="/about">{t[lang].about}</Link></li>
        <li><Link to="/services">{t[lang].services}</Link></li>
        <li><Link to="/projects">{t[lang].projects}</Link></li>
        <li><Link to="/contact">{t[lang].contact}</Link></li>
      </ul>
      <div className="controls">
        <button onClick={toggleLang}>{lang === 'ar' ? 'English' : 'العربية'}</button>
        <button onClick={toggleTheme}>{dark ? '☀️' : '🌙'}</button>
      </div>
    </nav>
  );
}
