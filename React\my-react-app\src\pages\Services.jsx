import React, { useState, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LanguageContext } from '../context/LanguageContext';

// دالة ترجمة بسيطة حسب اللغة الحالية
const t = (ar, en, lang) => (lang === 'ar' ? ar : en);

// SVGs متحركة
const AnimatedSVG1 = () => (
  <motion.svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg"
    initial="hidden" animate="visible">
    <motion.circle
      cx="32" cy="32" r="28" stroke="#FFD54F" strokeWidth="4"
      variants={{
        hidden: { pathLength: 0, opacity: 0 },
        visible: {
          pathLength: 1,
          opacity: 1,
          transition: { duration: 2, repeat: Infinity, repeatType: "reverse" },
        },
      }}
    />
    <motion.circle
      cx="32" cy="32" r="10" fill="#FFD54F"
      variants={{
        hidden: { scale: 0, opacity: 0 },
        visible: {
          scale: 1,
          opacity: 1,
          transition: { duration: 1.5, repeat: Infinity, repeatType: "mirror" },
        },
      }}
    />
  </motion.svg>
);

const AnimatedSVG2 = () => (
  <motion.svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg"
    initial="hidden" animate="visible">
    <motion.rect
      x="12" y="12" width="40" height="40" stroke="#4FC3F7" strokeWidth="4" rx="8" ry="8"
      variants={{
        hidden: { pathLength: 0, opacity: 0 },
        visible: {
          pathLength: 1,
          opacity: 1,
          transition: { duration: 3, repeat: Infinity, repeatType: "reverse" },
        },
      }}
    />
    <motion.circle
      cx="32" cy="32" r="8" fill="#4FC3F7"
      variants={{
        hidden: { scale: 0, opacity: 0 },
        visible: {
          scale: 1,
          opacity: 1,
          transition: { duration: 2, repeat: Infinity, repeatType: "mirror" },
        },
      }}
    />
  </motion.svg>
);

const AnimatedSVG3 = () => (
  <motion.svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg"
    initial="hidden" animate="visible">
    <motion.path
      d="M16 32 L48 32" stroke="#81C784" strokeWidth="4" strokeLinecap="round"
      variants={{
        hidden: { pathLength: 0, opacity: 0 },
        visible: {
          pathLength: 1,
          opacity: 1,
          transition: { duration: 1.5, repeat: Infinity, repeatType: "reverse" },
        },
      }}
    />
    <motion.circle
      cx="32" cy="32" r="12" fill="#81C784"
      variants={{
        hidden: { scale: 0, opacity: 0 },
        visible: {
          scale: 1,
          opacity: 1,
          transition: { duration: 2, repeat: Infinity, repeatType: "mirror" },
        },
      }}
    />
  </motion.svg>
);

const AnimatedSVG4 = () => (
  <motion.svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg"
    initial="hidden" animate="visible">
    <motion.polygon
      points="32,8 56,56 8,56"
      stroke="#F06292" strokeWidth="4" fill="none"
      variants={{
        hidden: { pathLength: 0, opacity: 0 },
        visible: {
          pathLength: 1,
          opacity: 1,
          transition: { duration: 2.5, repeat: Infinity, repeatType: "reverse" },
        },
      }}
    />
    <motion.circle
      cx="32" cy="40" r="8" fill="#F06292"
      variants={{
        hidden: { scale: 0, opacity: 0 },
        visible: {
          scale: 1,
          opacity: 1,
          transition: { duration: 2, repeat: Infinity, repeatType: "mirror" },
        },
      }}
    />
  </motion.svg>
);

const AnimatedSVG5 = () => (
  <motion.svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg"
    initial="hidden" animate="visible">
    <motion.line
      x1="16" y1="16" x2="48" y2="48" stroke="#BA68C8" strokeWidth="4" strokeLinecap="round"
      variants={{
        hidden: { pathLength: 0, opacity: 0 },
        visible: {
          pathLength: 1,
          opacity: 1,
          transition: { duration: 2, repeat: Infinity, repeatType: "reverse" },
        },
      }}
    />
    <motion.circle
      cx="32" cy="32" r="14" fill="#BA68C8"
      variants={{
        hidden: { scale: 0, opacity: 0 },
        visible: {
          scale: 1,
          opacity: 1,
          transition: { duration: 2, repeat: Infinity, repeatType: "mirror" },
        },
      }}
    />
  </motion.svg>
);

const AnimatedSVG6 = () => (
  <motion.svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg"
    initial="hidden" animate="visible">
    <motion.ellipse
      cx="32" cy="32" rx="24" ry="16" stroke="#4DB6AC" strokeWidth="4"
      variants={{
        hidden: { pathLength: 0, opacity: 0 },
        visible: {
          pathLength: 1,
          opacity: 1,
          transition: { duration: 3, repeat: Infinity, repeatType: "reverse" },
        },
      }}
    />
    <motion.circle
      cx="32" cy="32" r="10" fill="#4DB6AC"
      variants={{
        hidden: { scale: 0, opacity: 0 },
        visible: {
          scale: 1,
          opacity: 1,
          transition: { duration: 2, repeat: Infinity, repeatType: "mirror" },
        },
      }}
    />
  </motion.svg>
);

// قائمة الخدمات
const servicesList = [
  {
    id: 1,
    svgIcon: <AnimatedSVG1 />,
    title_ar: 'تطوير البرمجيات',
    title_en: 'Software Development',
    desc_ar: 'تطوير تطبيقات ويب وموبايل مخصصة حسب طلب العميل.',
    desc_en: 'Developing custom web and mobile applications.',
    details_ar: 'نستخدم أحدث التقنيات لتطوير برمجيات قوية تلبي احتياجات عملائك.',
    details_en: 'We use cutting-edge technologies to build robust software tailored to your clients.',
  },
  {
    id: 2,
    svgIcon: <AnimatedSVG2 />,
    title_ar: 'الذكاء الاصطناعي',
    title_en: 'Artificial Intelligence',
    desc_ar: 'تطبيق حلول الذكاء الاصطناعي لتحسين الأعمال.',
    desc_en: 'Implementing AI solutions to improve business.',
    details_ar: 'نطور حلول ذكاء اصطناعي مخصصة لتحليل البيانات واتخاذ قرارات ذكية.',
    details_en: 'We build custom AI solutions for data analysis and smart decision-making.',
  },
  {
    id: 3,
    svgIcon: <AnimatedSVG3 />,
    title_ar: 'صيانة البرمجيات',
    title_en: 'Software Maintenance',
    desc_ar: 'دعم وصيانة مستمرة للبرمجيات لضمان استمراريتها.',
    desc_en: 'Continuous support and maintenance to ensure software longevity.',
    details_ar: 'نوفر دعمًا فنيًا وتحديثات منتظمة للحفاظ على جودة البرمجيات.',
    details_en: 'We provide technical support and regular updates to maintain high-quality software.',
  },
  {
    id: 4,
    svgIcon: <AnimatedSVG4 />,
    title_ar: 'خدمات الحوسبة السحابية',
    title_en: 'Cloud Computing Services',
    desc_ar: 'حلول سحابية مرنة وآمنة لبيئة عملك.',
    desc_en: 'Flexible and secure cloud solutions for your business.',
    details_ar: 'نقدم استضافة سحابية متطورة تضمن الأمان وسرعة الوصول.',
    details_en: 'We provide advanced cloud hosting that ensures data security and performance.',
  },
  {
    id: 5,
    svgIcon: <AnimatedSVG5 />,
    title_ar: 'الأمن السيبراني',
    title_en: 'Cybersecurity',
    desc_ar: 'حماية متقدمة ضد التهديدات الرقمية.',
    desc_en: 'Advanced protection against digital threats.',
    details_ar: 'نراقب ونحلل أنظمة الأمان لمنع الاختراقات وتأمين البيانات.',
    details_en: 'We monitor and analyze security systems to prevent breaches and protect your data.',
  },
  {
    id: 6,
    svgIcon: <AnimatedSVG6 />,
    title_ar: 'تطوير تطبيقات الموبايل',
    title_en: 'Mobile App Development',
    desc_ar: 'تصميم وتطوير تطبيقات موبايل مبتكرة وسهلة الاستخدام.',
    desc_en: 'Designing and developing innovative and user-friendly mobile apps.',
    details_ar: 'نبني تطبيقات متوافقة مع جميع الأجهزة لتجربة مستخدم ممتازة.',
    details_en: 'We build apps compatible with all devices, delivering an excellent user experience.',
  },
];

export default function Services() {
  const { lang } = useContext(LanguageContext);
  const [selectedService, setSelectedService] = useState(null);

  return (
    <section className="page services" dir={lang === 'ar' ? 'rtl' : 'ltr'}>
      <h1 className="services-title">{t('خدماتنا', 'Our Services', lang)}</h1>
      <div className="services-grid">
        {servicesList.map((service) => (
          <motion.div
            key={service.id}
            className="service-card"
            whileHover={{ scale: 1.05 }}
            onClick={() => setSelectedService(service)}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: service.id * 0.1 }}
          >
            <motion.div className="icon">{service.svgIcon}</motion.div>
            <h3>{t(service.title_ar, service.title_en, lang)}</h3>
            <p>{t(service.desc_ar, service.desc_en, lang)}</p>
            <button
              className="btn-learn-more"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedService(service);
              }}
            >
              {t('تعرف أكثر', 'Learn More', lang)}
            </button>
          </motion.div>
        ))}
      </div>

      <AnimatePresence>
        {selectedService && (
          <motion.div
            className="modal-backdrop"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedService(null)}
          >
            <motion.div
              className="modal-content"
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: 50, opacity: 0 }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              onClick={(e) => e.stopPropagation()}
            >
              <button className="modal-close" onClick={() => setSelectedService(null)}>
                &times;
              </button>
              <motion.div
                className="modal-icon"
                animate={{ rotate: 360 }}
                transition={{ repeat: Infinity, duration: 10, ease: 'linear' }}
              >
                {selectedService.svgIcon}
              </motion.div>
              <h2>{t(selectedService.title_ar, selectedService.title_en, lang)}</h2>
              <p>{t(selectedService.details_ar, selectedService.details_en, lang)}</p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
}
