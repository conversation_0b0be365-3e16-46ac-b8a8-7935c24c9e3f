/* 🌐 الصفحة العامة */
.page.about {
  padding: 50px 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #e0e7ff 0%, #f8f9fa 100%);
  color: #1e1e2f;
  min-height: 100vh;
}

/* 🧑‍🤝‍🧑 عنوان فريق العمل */
.page.about h2 {
  text-align: center;
  font-size: 2.4rem;
  margin: 50px 0 30px;
  color: #2a2a72;
  font-weight: 700;
  letter-spacing: 1.2px;
  text-transform: uppercase;
  text-shadow: 1px 1px 4px rgba(42, 42, 114, 0.3);
}

/* 👥 حاوية البطاقات */
.employees-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 35px;
  margin-top: 20px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

/* 🪪 البطاقة الفردية */
.employee-card {
  width: 300px;
  background: #fff;
  border-radius: 18px;
  box-shadow:
    0 10px 20px rgba(42, 42, 114, 0.12),
    0 6px 6px rgba(42, 42, 114, 0.08);
  overflow: hidden;
  transition: transform 0.4s ease, box-shadow 0.4s ease;
  position: relative;
  cursor: pointer;
}

.employee-card:hover {
  transform: translateY(-12px) scale(1.05);
  box-shadow:
    0 20px 40px rgba(42, 42, 114, 0.25),
    0 12px 12px rgba(42, 42, 114, 0.15);
}

/* 🖼️ صورة الموظف */
.employee-photo {
  width: 100%;
  height: 270px;
  object-fit: cover;
  display: block;
  filter: brightness(0.92);
  transition: filter 0.3s ease;
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 18px;
}

.employee-card:hover .employee-photo {
  filter: brightness(1);
}

/* 📄 معلومات الموظف */
.employee-details {
  padding: 22px 20px 28px;
  text-align: center;
  background: linear-gradient(135deg, #f0f4ff 0%, #ffffff 100%);
  border-top-left-radius: 18px;
  border-top-right-radius: 18px;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.employee-name {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 6px;
  color: #2a2a72;
  letter-spacing: 0.03em;
}

.employee-role {
  font-size: 1.05rem;
  color: #e63946;
  margin-bottom: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.06em;
}

.employee-bio {
  font-size: 1rem;
  color: #4a4a6a;
  line-height: 1.5;
  font-weight: 400;
  font-style: italic;
  flex-grow: 1;
  margin-bottom: 0;
}

/* 📱 استجابة للموبايل */
@media (max-width: 768px) {
  .employee-card {
    width: 80%;
  }
}

@media (max-width: 400px) {
  .employee-card {
    width: 100%;
  }
}
