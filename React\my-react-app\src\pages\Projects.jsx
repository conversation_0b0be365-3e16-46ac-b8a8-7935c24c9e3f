import React, { useState, useContext } from 'react';
import { LanguageContext } from '../context/LanguageContext'; // تأكد من المسار حسب مشروعك

import { motion, AnimatePresence } from 'framer-motion';

const projectsList = [
  {
    id: 1,
    title_ar: 'نظام إدارة الموارد البشرية',
    title_en: 'HR Management System',
    desc_ar: 'نظام متكامل لإدارة الموارد البشرية بكفاءة عالية.',
    desc_en: 'Comprehensive system for efficient HR management.',
    details_ar: 'هذا النظام يوفر حلول متكاملة لإدارة الموظفين، الرواتب، الإجازات، والتقارير بشكل سلس وفعال.',
    details_en: 'This system offers integrated solutions for employee management, payroll, leaves, and reporting efficiently.',
    img: '/images/istockphoto-2157116331-612x612.jpg',
  },
  {
    id: 2,
    title_ar: 'تطبيق تتبع المبيعات',
    title_en: 'Sales Tracking App',
    desc_ar: 'تطبيق لتحليل ومتابعة بيانات المبيعات بدقة.',
    desc_en: 'Application for precise sales data analysis and tracking.',
    details_ar: 'يسمح التطبيق للإدارة بمراقبة المبيعات اليومية، تحليل الأداء، وتحديد فرص النمو بسهولة.',
    details_en: 'The app enables management to monitor daily sales, analyze performance, and identify growth opportunities easily.',
    img: '/images/istockphoto-2166223667-612x612.jpg',
  },
  {
    id: 3,
    title_ar: 'منصة تعليم إلكترونية',
    title_en: 'E-learning Platform',
    desc_ar: 'منصة متقدمة للدروس والمحاضرات عن بُعد.',
    desc_en: 'Advanced platform for remote lessons and lectures.',
    details_ar: 'توفر المنصة أدوات تفاعلية للتعليم عن بعد، مثل الفيديوهات، الواجبات، والاختبارات المدمجة.',
    details_en: 'The platform offers interactive tools for remote learning, including videos, assignments, and integrated quizzes.',
    img: '/images/istockphoto-1717676205-612x612.jpg',
  },
  {
    id: 4,
    title_ar: 'تطبيق توصيل الطلبات',
    title_en: 'Delivery App',
    desc_ar: 'تطبيق لتوصيل الطلبات بسرعة وفعالية.',
    desc_en: 'App for fast and efficient order delivery.',
    details_ar: 'يوفر التطبيق تجربة مستخدم سلسة مع تعقب الطلبات والتواصل المباشر مع السائقين.',
    details_en: 'The app provides a smooth user experience with order tracking and direct communication with drivers.',
    img: '/images/istockphoto-**********-612x612.jpg',
  },
  {
    id: 5,
    title_ar: 'نظام مراقبة الصحة',
    title_en: 'Health Monitoring System',
    desc_ar: 'نظام ذكي لمراقبة الحالة الصحية عن بعد.',
    desc_en: 'Smart system for remote health monitoring.',
    details_ar: 'يستخدم النظام أجهزة استشعار وتقنيات الذكاء الاصطناعي لتحليل البيانات الصحية وتحذير المستخدمين.',
    details_en: 'The system uses sensors and AI technologies to analyze health data and alert users.',
    img: '/images/istockphoto-**********-612x612.jpg',
  },
  {
    id: 6,
    title_ar: 'تطبيق جدولة الاجتماعات',
    title_en: 'Meeting Scheduler App',
    desc_ar: 'أداة متطورة لتنظيم وجدولة الاجتماعات بسهولة.',
    desc_en: 'Advanced tool for easy meeting scheduling.',
    details_ar: 'يساعد التطبيق على جدولة الاجتماعات وتنبيه المشاركين مع التكامل مع تقاويم متعددة.',
    details_en: 'The app helps schedule meetings and notify participants with multi-calendar integration.',
    img: '/images/istockphoto-**********-612x612.jpg',
  },
];

export default function Projects() {
  const { lang } = useContext(LanguageContext);
  const [selectedProject, setSelectedProject] = useState(null);

  const openModal = (project) => {
    setSelectedProject(project);
  };

  const closeModal = () => {
    setSelectedProject(null);
  };

  const t = (ar, en) => (lang === 'ar' ? ar : en); // دالة بسيطة لتسهيل الترجمة

  return (
    <section className="page projects" dir={lang === 'ar' ? 'rtl' : 'ltr'}>
      <h1>{t('مشاريعنا', 'Our Projects')}</h1>

      <div className="projects-grid">
        {projectsList.map((proj) => (
          <div key={proj.id} className="project-card">
            <div className="img-wrapper">
              <img
                src={proj.img}
                alt={t(proj.title_ar, proj.title_en)}
              />
              <div className="overlay">
                <h3>{t(proj.title_ar, proj.title_en)}</h3>
                <p>{t(proj.desc_ar, proj.desc_en)}</p>
                <button className="btn-details" onClick={() => openModal(proj)}>
                  {t('تفاصيل أكثر', 'More Details')}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* ✅ Modal with animation */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            className="modal-backdrop"
            onClick={closeModal}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="modal-content"
              onClick={(e) => e.stopPropagation()}
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <button className="modal-close" onClick={closeModal}>
                &times;
              </button>
              <img
                src={selectedProject.img}
                alt={t(selectedProject.title_ar, selectedProject.title_en)}
                className="modal-img"
              />
              <h2>{t(selectedProject.title_ar, selectedProject.title_en)}</h2>
              <p className="modal-desc">
                {t(selectedProject.details_ar, selectedProject.details_en)}
              </p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
}
