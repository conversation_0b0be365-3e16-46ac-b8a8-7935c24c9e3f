/* الصفحة */
.page.home {
  font-family: 'Cairo', sans-serif;
  color: #333;
  line-height: 1.6;
}

/* ✅ TOP ALERT */
.top-alert {
  background-color: #ff6b6b;
  color: white;
  text-align: center;
  padding: 10px;
  font-weight: bold;
  font-size: 0.95rem;
}

/* ✅ HERO SECTION */
.hero-section {
  background: linear-gradient(to bottom right, #00b894, #0984e3);
  color: white;
  padding: 100px 20px;
  text-align: center;
}

.hero-section h1 {
  font-size: 2.8rem;
  margin-bottom: 20px;
}

.hero-section p {
  font-size: 1.2rem;
  margin-bottom: 30px;
}

.cta-button {
  background-color: #fff;
  color: #00b894;
  padding: 12px 25px;
  border-radius: 8px;
  font-weight: bold;
  text-decoration: none;
  transition: background 0.3s ease;
}

.cta-button:hover {
  background-color: #f1f1f1;
}

/* ✅ VALUES SECTION */
.values-section {
  padding: 60px 20px;
  background-color: #f9f9f9;
  text-align: center;
}

.values-section h2 {
  font-size: 2rem;
  margin-bottom: 20px;
}

.values-section ul {
  list-style: none;
  padding: 0;
}

.values-section li {
  margin: 10px 0;
  font-size: 1.1rem;
}

/* ✅ ABOUT */
.about-preview {
  padding: 60px 20px;
  text-align: center;
}

.link-button {
  margin-top: 20px;
  display: inline-block;
  color: #00b894;
  text-decoration: underline;
}

/* ✅ SERVICES SLIDER */
.services-preview {
  padding: 60px 20px;
  background-color: #fff;
  text-align: center;
}

.service-slider .card {
  background: white;
  padding: 30px 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: 10px;
  text-align: center;
  transition: transform 0.3s ease;
}

.service-slider .card:hover {
  transform: translateY(-8px);
}

.service-slider .card h3 {
  margin-top: 10px;
  color: #333;
}

.service-slider .card p {
  font-size: 0.95rem;
  color: #666;
}

/* ✅ WORKFLOW SECTION */
.workflow-section {
  padding: 60px 20px;
  background-color: #f9f9f9;
  text-align: center;
}

.workflow-steps {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 25px;
  margin-top: 30px;
}

.step {
  background: white;
  padding: 25px;
  width: 220px;
  border-radius: 10px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.step svg {
  color: #00b894;
  font-size: 24px;
  margin-bottom: 10px;
}

/* ✅ CASE STUDIES */
.case-studies {
  padding: 60px 20px;
  background-color: #fff;
  text-align: center;
}

.case-studies h2 {
  margin-bottom: 30px;
}

.case {
  background: #f3f3f3;
  padding: 30px;
  border-radius: 10px;
  max-width: 700px;
  margin: auto;
}

/* ✅ BLOG SECTION */
.blog-section {
  padding: 60px 20px;
  text-align: center;
  background: #f9f9f9;
}

.blog-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 25px;
  margin-top: 20px;
}

.blog-post {
  background: white;
  padding: 20px;
  width: 280px;
  border-radius: 10px;
  box-shadow: 0 3px 8px rgba(0,0,0,0.05);
}

.blog-post h3 {
  margin-bottom: 10px;
  color: #00b894;
}

.blog-post p {
  font-size: 0.95rem;
  color: #555;
}

.blog-post a {
  display: inline-block;
  margin-top: 10px;
  color: #0984e3;
  text-decoration: underline;
}

/* ✅ CONTACT CTA */
.contact-cta {
  padding: 60px 20px;
  background-color: #00b894;
  color: white;
  text-align: center;
}

/* ✅ FLOATING CTA */
.floating-cta {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #00b894;
  color: white;
  padding: 12px 18px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: bold;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  z-index: 999;
}

/* ✅ RESPONSIVE */
@media (max-width: 768px) {
  .workflow-steps,
  .blog-list {
    flex-direction: column;
    align-items: center;
  }

  .service-slider .card {
    width: 90%;
    margin: 0 auto;
  }
}

/* TESTIMONIALS */
.testimonials-section {
  padding: 60px 20px;
  background-color: #f2f2f2;
  text-align: center;
}

.testimonials {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 30px;
}

.testimonial {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  max-width: 400px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.testimonial p {
  font-style: italic;
  color: #444;
}

.testimonial strong {
  display: block;
  margin-top: 10px;
  font-weight: bold;
}

/* FAQ */
.faq-section {
  padding: 60px 20px;
  background-color: #fff;
  text-align: center;
}

.faq-section details {
  margin: 10px auto;
  max-width: 700px;
  background: #f9f9f9;
  padding: 15px 20px;
  border-radius: 10px;
  box-shadow: 0 0 8px rgba(0,0,0,0.05);
  cursor: pointer;
}

.faq-section summary {
  font-weight: bold;
  font-size: 1.05rem;
}

/* MAP SECTION */
.map-section {
  padding: 60px 20px;
  background-color: #f5f5f5;
  text-align: center;
}

.map-container iframe {
  width: 100%;
  border-radius: 10px;
  border: none;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}
.workflow-section {
  padding: 60px 20px;
  background-color: #f9f9f9;
  text-align: center;
}

.workflow-section h2 {
  font-size: 2.2rem;
  margin-bottom: 40px;
}

.workflow-steps {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
}

.step {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 30px 20px;
  width: 250px;
  text-align: center;
  transition: transform 0.3s ease;
}

.step:hover {
  transform: translateY(-10px);
}

.step span {
  background-color: #00b894;
  color: white;
  font-size: 1.5rem;
  padding: 16px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.step h3 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: #333;
}

.step p {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.6;
}
.case-studies-section {
  padding: 60px 20px;
  background: #f9fbff;
  text-align: center;
}

.case-studies-section h2 {
  font-size: 2.2rem;
  margin-bottom: 30px;
  color: #222;
}

.case-studies-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.case-study-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  border-left: 5px solid #007bff;
  text-align: start;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.case-study-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.icon-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
  margin-bottom: 5px;
}

.icon-title h3 {
  margin: 0;
  font-size: 1.15rem;
  color: #333;
}

.icon {
  font-size: 1.4rem;
}

/* تحسين النص داخل كل مرحلة */
.case-study-card p {
  margin-bottom: 15px;
  line-height: 1.6;
  color: #555;
}

/* ألوان مخصصة للخط الجانبي حسب المرحلة */
.case-study-card:nth-child(1) {
  border-left-color: #17a2b8; /* أزرق فاتح */
}
.case-study-card:nth-child(2) {
  border-left-color: #28a745; /* أخضر */
}
html[dir="rtl"] .case-study-card {
  text-align: right;
  border-left: none;
  border-right: 5px solid #007bff;
}
.blog-list {
  display: flex;
  
  gap: 0.85rem;
  max-width: 900px;
  margin: 0 auto;
  padding: 0 1rem;
}

.blog-post {
  cursor: pointer;
  border: 1px solid #ddd;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 4px 8px rgba(0,0,0,0.05);
  transition: box-shadow 0.3s ease, background-color 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.blog-post:hover {
  box-shadow: 0 8px 20px rgba(0, 123, 255, 0.25);
  background-color: #f0f8ff;
}

.blog-post.open {
  background-color: #d9eefd;
  box-shadow: 0 10px 25px rgba(0, 123, 255, 0.4);
}

.blog-post h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.4rem;
  font-weight: 700;
  color: #003366;
  user-select: none;
}

.blog-post .read-more {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-weight: 700;
  color: #007bff;
  font-size: 0.95rem;
  user-select: none;
  transition: color 0.3s ease;
}

.blog-post:hover .read-more {
  color: #0056b3;
}

.blog-post p {
  margin-top: 0;
  line-height: 1.65;
  color: #444;
  font-size: 1rem;
  user-select: text;
  white-space: pre-line;
}

/* أيقونة الكتاب */
.blog-post h3 svg {
  color: #007bff;
  flex-shrink: 0;
}

/* السهم داخل الـ read-more */
.blog-post .read-more svg {
  font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 600px) {
  .blog-post {
    padding: 1rem 1.25rem;
  }
  .blog-post h3 {
    font-size: 1.2rem;
  }
  .blog-post p {
    font-size: 0.95rem;
  }
}




/* RobotsGallery.css */

.robots-gallery {
  padding: 2.5rem 1rem;
  text-align: center;
  background: linear-gradient(to bottom, #fdfdfd, #f1f1f1);
}

.robots-gallery h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #222;
}

.robots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 1.5rem;
  padding: 0 1rem;
}

.robot-card {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0,0,0,0.08);
  transition: all 0.3s ease-in-out;
  border: 1px solid #e0e0e0;
  cursor: pointer;
}

.robot-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.15);
}

.robot-card img {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-bottom: 1px solid #ddd;
}

.robot-card h3 {
  font-size: 1.2rem;
  color: #333;
  font-weight: 600;
  margin: 1rem 0 0.5rem;
  padding: 0 1rem;
}

.robot-card p {
  font-size: 0.95rem;
  color: #555;
  padding: 0 1rem 1.2rem;
  line-height: 1.4;
}

/* Responsive Tweaks */
@media (max-width: 500px) {
  .robots-gallery h2 {
    font-size: 1.6rem;
  }

  .robot-card h3 {
    font-size: 1.1rem;
  }

  .robot-card p {
    font-size: 0.88rem;
  }
}


