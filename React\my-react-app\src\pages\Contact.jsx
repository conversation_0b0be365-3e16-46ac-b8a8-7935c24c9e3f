import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { LanguageContext } from '../context/LanguageContext';

export default function Contact() {
  const { lang } = useContext(LanguageContext);

  const texts = {
    ar: {
      title: "تواصل معنا",
      nameLabel: "الاسم:",
      namePlaceholder: "اسمك الكامل",
      emailLabel: "البريد الإلكتروني:",
      emailPlaceholder: "<EMAIL>",
      messageLabel: "رسالتك:",
      messagePlaceholder: "اكتب رسالتك هنا...",
      submit: "إرسال",
      contactInfo: "أو تواصل معنا عبر: <EMAIL>",
    },
    en: {
      title: "Contact Us",
      nameLabel: "Name:",
      namePlaceholder: "Your full name",
      emailLabel: "Email:",
      emailPlaceholder: "<EMAIL>",
      messageLabel: "Your message:",
      messagePlaceholder: "Write your message here...",
      submit: "Send",
      contactInfo: "Or contact us at: <EMAIL>",
    },
  };

  const t = texts[lang] || texts.ar;

  return (
    <motion.div 
      className="page contact"
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <motion.h1 
        initial={{ opacity: 0, y: -20 }} 
        animate={{ opacity: 1, y: 0 }} 
        transition={{ duration: 0.5 }}
      >
        {t.title}
      </motion.h1>

      <motion.form
        initial="hidden"
        animate="visible"
        variants={{
          hidden: {},
          visible: {
            transition: {
              staggerChildren: 0.2
            }
          }
        }}
      >
        {[ 
          { label: t.nameLabel, type: 'text', placeholder: t.namePlaceholder },
          { label: t.emailLabel, type: 'email', placeholder: t.emailPlaceholder }
        ].map(({ label, type, placeholder }, i) => (
          <motion.div 
            key={i} 
            className="form-group"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: i * 0.2 }}
          >
            <label>{label}</label>
            <input type={type} placeholder={placeholder} />
          </motion.div>
        ))}

        <motion.div 
          className="form-group"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4, delay: 0.4 }}
        >
          <label>{t.messageLabel}</label>
          <textarea placeholder={t.messagePlaceholder} />
        </motion.div>

        <motion.button 
          type="submit"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.4, delay: 0.6 }}
        >
          {t.submit}
        </motion.button>
      </motion.form>

      <motion.p 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.8 }}
      >
        {t.contactInfo}
      </motion.p>
    </motion.div>
  );
}
